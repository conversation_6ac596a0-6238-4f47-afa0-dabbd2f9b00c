<template>
  <div class="payment-page">
    <div class="payment-container">

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 主要内容 -->
      <template v-else>

        <!-- 浏览器场景：直接支付 -->
        <div v-if="isBrowserEnvironment" class="browser-payment-section">
         
          <div class="payment-actions">
            <el-button 
              type="danger" 
              size="large" 
              @click="createOrderAndPay" 
              :loading="paymentLoading"
              class="pay-button"
            >
              <i class="el-icon-shopping-cart-2"></i> 
              立即支付 ¥{{ getCurrentPrice() }}
            </el-button>
          </div>

          <!-- 订单状态显示 -->
          <div v-if="orderInfo.orderNo" class="order-status">
            <div class="status-item">
              <span>订单号：</span>
              <span>{{ orderInfo.orderNo }}</span>
            </div>
            <div class="status-item">
              <span>创建时间：</span>
              <span>{{ orderInfo.createdTime }}</span>
            </div>
            <div v-if="orderInfo.remainingTime > 0" class="countdown">
              <span>剩余时间：</span>
              <span class="countdown-time">{{ formatTime(orderInfo.remainingTime) }}</span>
            </div>
          </div>
        </div>

        <!-- 其他场景：引导页面 -->
        <div v-else class="guide-payment-section">
          <div class="guide-content">
            <div class="guide-image">
              <img 
                :src="guideImageUrl" 
                alt="支付引导图" 
                class="guide-img"
              />
            </div>
            <div class="guide-actions">
              <el-button 
                type="primary" 
                @click="copyCurrentUrl"
                class="copy-btn"
              >
                <i class="el-icon-document-copy" style="background: green;"></i> 复制链接
              </el-button>
            </div>


          </div>

        
          <!-- 实时查询状态 -->
          <div v-show="false" class="query-status-section">
            <div class="query-header">
              <h3>购买状态实时查询</h3>
              <el-button
                type="primary"
                size="small"
                @click="checkPurchaseStatus"
                :loading="queryLoading"
              >
                <i class="el-icon-refresh"></i> 立即查询
              </el-button>
            </div>

            <div class="query-progress">
              <div class="progress-bar">
                <div class="progress-inner" :style="{ width: `${(queryCount % 30) * 3.33}%` }"></div>
              </div>
              <div class="progress-text">
                <span>自动查询中</span>
                <span>{{ Math.floor(queryCount / 10) }}s 后刷新</span>
              </div>
            </div>

            <div class="query-message">
              <i class="el-icon-refresh query-icon"></i>
              <span>正在检测您的购买状态，支付成功后将自动跳转...</span>
            </div>
          </div>
        </div>

        <!-- 支付成功提示 -->
        <div v-if="isPaid" class="success-notice">
          <div class="success-icon">
            <i class="el-icon-check"></i>
          </div>
          <div class="success-title">支付成功</div>
          <div class="success-text">您已成功支付，可以立即观看视频</div>
          <el-button type="primary" @click="goToPlayPage">立即观看</el-button>
        </div>

        <!-- 订单超时提示 -->
        <div v-if="isExpired" class="expired-notice">
          <div class="expired-icon">
            <i class="el-icon-time"></i>
          </div>
          <div class="expired-title">订单已超时</div>
          <div class="expired-text">该订单已超过支付时间，请重新创建订单</div>
          <el-button type="primary" @click="resetOrder">创建新订单</el-button>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getVideo } from '../../api/resources'
import { createOrder, quickPayment, checkOrderStatus, checkVideoPurchased, getPendingOrdersByIp, storePendingPayment } from '../../api/frontend'
import { decryptPrice } from '../../utils/crypto'
import { getApiBaseUrl } from '../../utils/runtime-config'
import { buildPlayUrl } from '../../utils/urlHelper'
import { isIOSDevice, isInBrowser, convertPlatformPathToDirectPath } from '../../utils/iframe'

// 路由和基础配置
const route = useRoute()
const router = useRouter()
const apiBaseUrl = getApiBaseUrl()

// 响应式数据
const loading = ref(true)
const paymentLoading = ref(false)
const queryLoading = ref(false)
const video = ref({})
const isPaid = ref(false)
const isExpired = ref(false)

// 订单信息
const orderInfo = ref({
  orderNo: '',
  orderId: null,
  token: '',
  createdTime: '',
  remainingTime: 300 // 5分钟超时
})

// 查询相关
const queryCount = ref(0)
const checkInterval = ref(null)
const timeoutInterval = ref(null)

// 未支付订单相关
const pendingOrder = ref(null)
const pendingLoading = ref(false)

// 计算属性
const isBrowserEnvironment = computed(() => {
  return detectBrowserEnvironment()
})

const guideImageUrl = computed(() => {
  return `${apiBaseUrl}/uploads/images/123123.jpg`
})

// 获取当前价格
const getCurrentPrice = () => {
  const subscriptionType = route.query.subscription_type || 'single'
  if (subscriptionType === 'single') {
    return Math.floor(video.value.price || 0)
  } else if (subscriptionType === 'day') {
    return Math.floor(video.value.day_price || 0)
  } else if (subscriptionType === 'week') {
    return Math.floor(video.value.week_price || 0)
  } else if (subscriptionType === 'month') {
    return Math.floor(video.value.month_price || 0)
  }
  return 0
}

// 格式化时间
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化日期时间
const formatDateTime = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 检测浏览器环境
const detectBrowserEnvironment = () => {
  // 使用工具函数检测浏览器环境
  const inBrowser = isInBrowser()

  // 特殊处理：iOS设备在浏览器中且是/p路径，需要进行路径转换
  if (isIOSDevice() && inBrowser) {
    const currentPath = window.location.hash ? window.location.hash.substring(1) : window.location.pathname
    if (currentPath.startsWith('/p/')) {
      console.log('iOS设备检测到/p路径，需要进行路径转换')
      handleIOSPathConversion()
      return false // 暂时返回false，等待路径转换完成
    }
  }

  // 如果在浏览器中且不在iframe中，认为是浏览器环境
  return inBrowser
}

// 处理iOS设备的路径转换
const handleIOSPathConversion = () => {
  try {
    const convertedUrl = convertPlatformPathToDirectPath(window.location.href)
    if (convertedUrl !== window.location.href) {
      console.log('iOS设备路径转换:', window.location.href, '->', convertedUrl)
      ElMessage({
        type: 'info',
        message: 'iOS设备检测，正在优化访问路径...',
        duration: 2000
      })

      setTimeout(() => {
        window.location.href = convertedUrl
      }, 1000)
    }
  } catch (error) {
    console.error('iOS路径转换失败:', error)
  }
}

// 获取视频信息
const fetchVideoInfo = async () => {
  try {
    loading.value = true
    const videoId = route.params.id

    if (!videoId) {
      ElMessage.error('视频ID不能为空')
      goBack()
      return
    }

    const response = await getVideo(videoId)

    if (response.success) {
      video.value = response.data
      
      // 处理加密价格
      const encryptedPrice = route.query.price_data
      if (encryptedPrice) {
        try {
          const decryptedPrice = decryptPrice(encryptedPrice, 9.9)
          if (decryptedPrice && decryptedPrice > 0) {
            video.value.price = decryptedPrice
          }
        } catch (error) {
          console.error('价格解密失败:', error)
        }
      }
    } else {
      ElMessage.error(response.message || '获取视频信息失败')
      goBack()
    }
  } catch (error) {
    console.error('获取视频信息失败:', error)
    ElMessage.error('获取视频信息失败，请重试')
    goBack()
  } finally {
    loading.value = false
  }
}

// 创建订单并支付（浏览器场景）
const createOrderAndPay = async () => {
  try {
    // 防止重复点击
    if (paymentLoading.value) {
      console.log('支付正在进行中，忽略重复请求')
      return
    }

    paymentLoading.value = true
    console.log('=== 开始创建订单并支付 ===')

    // 检查是否有预创建的订单
    const preCreatedOrder = localStorage.getItem('preCreatedOrder')
    if (preCreatedOrder) {
      try {
        const orderData = JSON.parse(preCreatedOrder)
        console.log('发现预创建订单，使用快速支付:', orderData.orderNo)

        // 如果当前orderInfo为空，先恢复订单信息
        if (!orderInfo.value.orderNo && orderData.orderNo) {
          orderInfo.value = {
            orderNo: orderData.orderNo,
            orderId: orderData.orderId,
            token: orderData.token,
            createdTime: orderData.createdTime,
            remainingTime: 300,
            paymentUrl: orderData.paymentUrl,
            responseData: orderData.responseData
          }
        }

        // 调用快速支付API
        const quickPayResponse = await quickPayment({
          order_no: orderData.orderNo,
          platform: route.query.platform
        })

        if (quickPayResponse && quickPayResponse.success) {
          console.log('快速支付响应:', quickPayResponse)
          await handlePaymentRedirect(quickPayResponse.data)
          return
        } else {
          // 快速支付API返回失败，不降级到普通支付
          console.error('快速支付失败:', quickPayResponse?.message || '未知错误')
          ElMessage.error(`快速支付失败: ${quickPayResponse?.message || '未知错误'}`)
          // 清除无效的预创建订单
          localStorage.removeItem('preCreatedOrder')
          orderInfo.value = { orderNo: '', orderId: '', token: '', createdTime: '', remainingTime: 0 }
          return
        }
      } catch (error) {
        console.error('快速支付请求失败:', error.message)
        ElMessage.error(`快速支付请求失败: ${error.message}`)
        // 清除无效的预创建订单
        localStorage.removeItem('preCreatedOrder')
        orderInfo.value = { orderNo: '', orderId: '', token: '', createdTime: '', remainingTime: 0 }
        return
      }
    }

    // 验证必要参数
    if (!video.value.id) {
      throw new Error('视频ID缺失')
    }
    if (!route.query.code) {
      throw new Error('链接代码不能为空，请通过正确的推广链接访问')
    }

    // 创建订单参数
    const orderParams = {
      video_id: video.value.id,
      payment_method: 'wechat',
      agent_code: route.query.code,
      platform: route.query.platform,
      price: getCurrentPrice(), // 使用统一的价格计算函数
      subscription_type: route.query.subscription_type || 'single'
    }

    // 添加支付通道ID（如果有）
    if (route.query.payment_channel_id) {
      orderParams.payment_channel_id = parseInt(route.query.payment_channel_id)
    }

    console.log('创建订单参数:', orderParams)

    // 调用创建订单API
    const response = await createOrder(orderParams)
    console.log('订单创建响应:', response)

    if (response && response.success) {
      // 保存订单信息
      orderInfo.value = {
        orderNo: response.data.order_no,
        orderId: response.data.order_id,
        token: response.data.token,
        createdTime: formatDateTime(new Date()),
        remainingTime: 300
      }

      // 开始订单状态检查和超时计时
      // startOrderStatusCheck()
      // startOrderTimeout()

      // 处理支付跳转
      await handlePaymentRedirect(response.data)

    } else {
      throw new Error(response?.message || '创建订单失败')
    }
  } catch (error) {
    console.error('创建订单失败:', error)
    ElMessage.error(error.message || '创建订单失败，请重试')
  } finally {
    paymentLoading.value = false
  }
}

// 处理支付跳转
const handlePaymentRedirect = async (orderData) => {
  try {
    // 存储支付信息到Redis（在跳转前）
    if (orderData.payment_url) {
      try {
        await storePendingPayment({
          payment_url: orderData.payment_url,
          order_info: {
            order_no: orderData.order_no,
            video_id: video.value.id,
            video_title: video.value.title,
            amount: getCurrentPrice(),
            subscription_type: route.query.subscription_type || 'single'
          }
        })
        console.log('支付信息已存储到Redis')
      } catch (redisError) {
        console.warn('存储支付信息到Redis失败:', redisError)
        // 不阻断支付流程
      }
    }

    // 处理不同类型的支付响应
    if (orderData.response_type === 'html_redirect') {
      // HTML跳转模式
      if (orderData.redirect_html) {
        document.open()
        document.write(orderData.redirect_html)
        document.close()
      } else if (orderData.payment_url) {
        window.location.href = orderData.payment_url
      }
    } else if (orderData.response_type === 'form_redirect') {
      // 表单跳转模式
      if (orderData.form_html) {
        document.open()
        document.write(orderData.form_html)
        document.close()
      }
    } else if (orderData.response_type === 'form_submit') {
      // 表单提交模式
      if (orderData.response_data) {
        document.open()
        document.write(orderData.response_data)
        document.close()
      }
    } else {
      // 普通支付模式
      if (orderData.payment_url) {
        window.location.href = orderData.payment_url;
      } else {
        throw new Error('支付链接缺失')
      }
    }
  } catch (error) {
    console.error('支付跳转失败:', error)
    ElMessage.error('支付跳转失败，请重试')
  }
}

// 检查购买状态（其他场景）
const checkPurchaseStatus = async () => {
  try {
    queryLoading.value = true

  

    const response = await checkVideoPurchased(video.value.id)
    console.log('购买状态查询结果:', response)

    if (response.success) {
      const { purchased, purchaseType, orderInfo: orderData } = response.data

      if (purchased && orderData) {
        // 检查订阅是否过期
        if (purchaseType !== 'single' && orderData.expires_at) {
          const expiresAt = new Date(orderData.expires_at)
          const now = new Date()

          if (expiresAt <= now) {
            ElMessage.warning('您的订阅已过期，请重新购买')
            return
          }
        }

        // 购买有效，设置状态并跳转
        isPaid.value = true
        orderInfo.value.token = orderData.token || ''

        // 停止轮询
        stopAllTimers()

        ElMessage.success('检测到有效购买记录，即将跳转到播放页面')

        // 跳转到播放页面
        setTimeout(() => {
          goToPlayPage()
        }, 1000)
      } else {
      
      }
    } else {
      ElMessage.error(response.message || '查询购买状态失败')
    }
  } catch (error) {
    console.error('检查购买状态失败:', error)
    ElMessage.error('检查购买状态失败，请重试')
  } finally {
    queryLoading.value = false
  }
}

// 开始订单状态检查
const startOrderStatusCheck = () => {
  queryCount.value = 0
  stopOrderStatusCheck()

  checkInterval.value = setInterval(() => {
    queryCount.value++

    if (isExpired.value) {
      stopOrderStatusCheck()
      return
    }

    // 每3秒查询一次
    // if (queryCount.value % 30 === 0) {
    //   checkOrderStatus()
    // }
  }, 100)
}

// 检查订单状态
const checkCurrentOrderStatus = async () => {
  if (isExpired.value || isPaid.value || !orderInfo.value.orderNo) {
    return
  }

  try {
    const response = await checkOrderStatus(orderInfo.value.orderNo)

    if (response.success) {
      if (response.data.status === 'paid') {
        isPaid.value = true
        orderInfo.value.token = response.data.token || ''

        stopAllTimers()
        ElMessage.success('支付成功！')

        setTimeout(() => {
          goToPlayPage()
        }, 1000)
      } else if (response.data.status === 'expired') {
        handleOrderTimeout()
      }
    }
  } catch (error) {
    console.error('检查订单状态失败:', error)
  }
}

// 停止订单状态检查
const stopOrderStatusCheck = () => {
  if (checkInterval.value) {
    clearInterval(checkInterval.value)
    checkInterval.value = null
  }
}


// 预创建订单以提升支付速度
const preCreateOrderForGuide = async () => {
  try {
    // 检查是否已有订单信息
    if (orderInfo.value.orderNo) {
      console.log('已存在订单，跳过预创建:', orderInfo.value.orderNo)
      return
    }

    // 验证必要参数
    if (!video.value.id || !route.query.code) {
      console.log('缺少必要参数，跳过预创建订单')
      return
    }

    console.log('开始预创建订单以提升支付速度...')

    // 创建订单参数
    const orderParams = {
      video_id: video.value.id,
      payment_method: 'wechat', // 默认使用微信支付
      agent_code: route.query.code,
      platform: route.query.platform,
      price: getCurrentPrice(), // 使用统一的价格计算函数
      subscription_type: route.query.subscription_type || 'single'
    }

    // 添加支付通道ID（如果有）
    if (route.query.payment_channel_id) {
      orderParams.payment_channel_id = parseInt(route.query.payment_channel_id)
    }

    console.log('预创建订单参数:', orderParams)

    // 调用创建订单API
    const response = await createOrder(orderParams)
    console.log('预创建订单响应:', response)

    if (response && response.success) {
      // 保存订单信息
      orderInfo.value = {
        orderNo: response.data.order_no,
        orderId: response.data.order_id,
        token: response.data.token,
        createdTime: formatDateTime(new Date()),
        remainingTime: 300,
        paymentUrl: response.data.payment_url,
        responseData: response.data
      }

      console.log('✅ 订单预创建成功:', orderInfo.value.orderNo)

      // 存储到本地存储，以便跨页面使用
      localStorage.setItem('preCreatedOrder', JSON.stringify({
        orderNo: orderInfo.value.orderNo,
        orderId: orderInfo.value.orderId,
        token: orderInfo.value.token,
        paymentUrl: orderInfo.value.paymentUrl,
        createdTime: orderInfo.value.createdTime,
        responseData: orderInfo.value.responseData
      }))

      // 开始订单状态检查和超时计时
      startOrderStatusCheck()
      startOrderTimeout()

    } else {
      console.warn('预创建订单失败:', response?.message || '未知错误')
    }
  } catch (error) {
    console.warn('预创建订单失败:', error.message)
    // 预创建失败不影响正常流程，用户仍可手动触发支付
  }
}

// 开始订单超时计时
const startOrderTimeout = () => {
  orderInfo.value.remainingTime = 300
  stopOrderTimeout()

  timeoutInterval.value = setInterval(() => {
    orderInfo.value.remainingTime--

    if (orderInfo.value.remainingTime <= 0) {
      handleOrderTimeout()
    }
  }, 1000)
}

// 停止订单超时计时
const stopOrderTimeout = () => {
  if (timeoutInterval.value) {
    clearInterval(timeoutInterval.value)
    timeoutInterval.value = null
  }
}

// 处理订单超时
const handleOrderTimeout = () => {
  stopAllTimers()
  isExpired.value = true
  ElMessage.warning('订单已超时，请重新创建订单')
}

// 停止所有计时器
const stopAllTimers = () => {
  stopOrderStatusCheck()
  stopOrderTimeout()
}

// 重置订单状态
const resetOrder = () => {
  stopAllTimers()
  orderInfo.value = {
    orderNo: '',
    orderId: null,
    token: '',
    createdTime: '',
    remainingTime: 300
  }
  isExpired.value = false
  isPaid.value = false
}

// 跳转到播放页面
const goToPlayPage = () => {
  const playUrl = buildPlayUrl(video.value.id, orderInfo.value.token, route)
  console.log('跳转到播放页面:', playUrl)
  router.push(playUrl)
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 复制当前URL
const copyCurrentUrl = () => {
  const currentUrl = window.location.href

  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(currentUrl)
      .then(() => {
        ElMessage.success('链接已复制到剪贴板，请在浏览器中打开')
      })
      .catch(() => {
        fallbackCopyToClipboard(currentUrl)
      })
  } else {
    fallbackCopyToClipboard(currentUrl)
  }
}

// 降级复制方案
const fallbackCopyToClipboard = (text) => {
  try {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)

    if (successful) {
      ElMessage.success('链接已复制到剪贴板，请在浏览器中打开')
    } else {
      ElMessage.error('复制失败，请手动复制链接')
    }
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败，请手动复制链接')
  }
}

// 检查Redis中的未支付订单
const checkPendingOrders = async () => {
  try {
    console.log('检查Redis中的未支付订单...')
    const response = await getPendingOrdersByIp()

    if (response && response.code === 1 && response.data) {
      console.log('发现未支付订单:', response.data)
      pendingOrder.value = response.data
     
    } else {
      console.log('没有未支付订单')
      pendingOrder.value = null
    }
  } catch (error) {
    console.error('检查未支付订单失败:', error)
    pendingOrder.value = null
  }
}

// 继续未支付订单的支付
const continuePendingPayment = async () => {
  if (!pendingOrder.value || !pendingOrder.value.payment_url) {
    ElMessage.error('未支付订单信息无效')
    return
  }

  try {
    pendingLoading.value = true
    console.log('继续支付未完成订单:', pendingOrder.value.payment_url)

    // 存储支付信息到Redis（更新时间戳）
    await storePendingPayment({
      payment_url: pendingOrder.value.payment_url,
      order_info: pendingOrder.value.order_info || {}
    })

    // 跳转到支付页面
    window.top.location.href = pendingOrder.value.payment_url

  } catch (error) {
    console.error('继续支付失败:', error)
    ElMessage.error('跳转支付页面失败，请重试')
  } finally {
    pendingLoading.value = false
  }
}

// 格式化未支付订单时间
const formatPendingTime = (timeStr) => {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return timeStr
  }
}

// 开始直接购买状态检查（其他场景）
const startDirectPurchaseCheck = () => {
  console.log('开始直接查询购买状态')

  // 立即检查一次
  checkPurchaseStatus()

  // 开始轮询检查
  checkInterval.value = setInterval(() => {
    queryCount.value++

    // 每3秒查询一次
    if (queryCount.value % 30 === 0) {
      checkPurchaseStatus()
    }
  }, 100)
}

// 检查URL中是否包含订单号
const checkOrderNoInUrl = () => {
  console.log('检查URL中的订单号:', route.query)

  if (route.query.order_no) {
    console.log('URL中包含订单号:', route.query.order_no)
    orderInfo.value.orderNo = route.query.order_no

    // 如果URL中包含创建时间，计算剩余时间
    if (route.query.created_time) {
      orderInfo.value.createdTime = route.query.created_time

      const createdTime = new Date(route.query.created_time)
      const now = new Date()
      const elapsedSeconds = Math.floor((now - createdTime) / 1000)

      // 如果订单已经超过超时时间，直接标记为过期
      if (elapsedSeconds >= 300) {
        console.log('订单已超时')
        isExpired.value = true
        orderInfo.value.remainingTime = 0
        ElMessage.warning('订单已超时，请重新创建订单')
        return true
      }

      // 计算剩余时间
      orderInfo.value.remainingTime = Math.max(0, 300 - elapsedSeconds)
      console.log('订单剩余时间:', orderInfo.value.remainingTime, '秒')
    } else {
      // 如果没有创建时间，使用当前时间
      orderInfo.value.createdTime = formatDateTime(new Date())
      orderInfo.value.remainingTime = 300
    }

    // 如果订单未过期，开始计时和查询
    if (!isExpired.value) {
      console.log('订单未过期，开始查询和计时')

      // 立即检查订单状态
      checkOrderStatus()

      // 开始轮询订单状态
      // startOrderStatusCheck()

      // 开始订单超时计时
      // startOrderTimeout()

    }

    return true
  }

  console.log('URL中不包含订单号')
  return false
}

// 检查预创建的订单
const checkPreCreatedOrder = () => {
  try {
    const preCreatedOrder = localStorage.getItem('preCreatedOrder')
    if (preCreatedOrder) {
      const orderData = JSON.parse(preCreatedOrder)

      // 检查订单是否过期（5分钟）
      const createdTime = new Date(orderData.createdTime)
      const now = new Date()
      const diffMinutes = (now - createdTime) / (1000 * 60)

      if (diffMinutes < 5) {
        console.log('发现有效的预创建订单:', orderData.orderNo)

        // 恢复订单信息
        orderInfo.value = {
          orderNo: orderData.orderNo,
          orderId: orderData.orderId,
          token: orderData.token,
          createdTime: orderData.createdTime,
          remainingTime: Math.max(0, 300 - Math.floor(diffMinutes * 60)),
          paymentUrl: orderData.paymentUrl,
          responseData: orderData.responseData
        }

        // 开始订单状态检查和超时计时
        // startOrderStatusCheck()
        // startOrderTimeout()

        return true
      } else {
        console.log('预创建订单已过期，清除缓存')
        localStorage.removeItem('preCreatedOrder')
      }
    }
  } catch (error) {
    console.error('检查预创建订单失败:', error)
    localStorage.removeItem('preCreatedOrder')
  }
  return false
}

// 组件挂载
onMounted(async () => {
  try {
    // 首先检查URL中是否包含订单号
    const hasOrderNo = checkOrderNoInUrl()

    // 检查是否有预创建的订单
    const hasPreCreatedOrder = !hasOrderNo && checkPreCreatedOrder()

    // 先获取视频信息，确保价格正确
    await fetchVideoInfo()

    // 根据环境决定处理方式
    if (isBrowserEnvironment.value) {
      console.log('检测到浏览器环境，使用直接支付模式')

      // 如果有预创建订单，直接使用快速支付
      if (hasPreCreatedOrder && route.query.auto_pay === 'true') {
        console.log('检测到预创建订单和auto_pay参数，直接使用快速支付')
        createOrderAndPay()
      }
      // 如果没有订单号且没有预创建订单，但有auto_pay参数，自动触发支付
      else if (!hasOrderNo && !hasPreCreatedOrder && route.query.auto_pay === 'true') {
        console.log('没有预创建订单，创建新订单并支付')
        createOrderAndPay()
      }
    } else {
      console.log('检测到WebView环境，使用引导模式')

      // 立即预创建订单（现在视频信息已加载，价格正确）
      if (!hasPreCreatedOrder) {
        console.log('🚀 立即开始预创建订单（视频信息已加载，价格正确）...')
        await preCreateOrderForGuide()
      }

      // 检查Redis中的未支付订单
      await checkPendingOrders()

      console.log('✅ 引导页初始化完成，订单已准备就绪')

      // 开始轮询购买状态
      startDirectPurchaseCheck()
    }
  } catch (error) {
    console.error('页面初始化失败:', error)
    ElMessage.error('页面初始化失败，请重试')
  }
})

// 组件卸载
onUnmounted(() => {
  stopAllTimers()
})
</script>

<style scoped>
.payment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.payment-container {
  max-width: 600px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow: hidden;
}

.payment-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  cursor: pointer;
  color: #ffffff;
  font-size: 14px;
  display: flex;
  align-items: center;
  background: #409eff;
  padding: 10px;
  border-radius: 10px;
}

.loading-container {
  padding: 20px 0;
}

/* 支付重要提示标语 */
.payment-notice-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 2px solid #e17055;
  border-radius: 12px;
  margin: 20px 0;
  box-shadow: 0 4px 15px rgba(225, 112, 85, 0.2);
  animation: noticeGlow 2s ease-in-out infinite alternate;
}

.notice-icon {
  margin-right: 15px;
  padding: 10px;
  background-color: #e17055;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(225, 112, 85, 0.3);
}

.notice-icon svg {
  width: 28px;
  height: 28px;
}

.notice-content {
  text-align: center;
  flex: 1;
}

.notice-title {
  font-size: 18px;
  font-weight: 700;
  color: #e17055;
  margin-bottom: 5px;
}

.notice-text {
  font-size: 16px;
  color: #2d3436;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes noticeGlow {
  0% {
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.2);
  }
  100% {
    box-shadow: 0 6px 25px rgba(225, 112, 85, 0.4);
  }
}

/* 浏览器支付区域 */
.browser-payment-section {
  margin-bottom: 20px;
}

.payment-info {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.payment-info h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.payment-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 20px;
  color: #f56c6c;
  font-weight: bold;
}

.payment-actions {
  text-align: center;
  margin-bottom: 20px;
}

.pay-button {
  width: 100%;
  height: 50px;
  font-size: 18px;
  font-weight: bold;
}

/* 订单状态 */
.order-status {
  background-color: #f0f9eb;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  border: 1px solid #e1f3d8;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.countdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #e1f3d8;
}

.countdown-time {
  color: #f56c6c;
  font-weight: bold;
  font-family: monospace;
  font-size: 16px;
}

/* 引导支付区域 */
.guide-payment-section {
  margin-bottom: 20px;
}

.guide-content {
  text-align: center;
}

.guide-image {
  margin-bottom: 20px;
}

.guide-img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.guide-actions {
  margin-bottom: 20px;
}

.copy-btn {
  width: 200px;
  height: 44px;
  font-size: 16px;
  font-weight: bold;
}

.guide-tips {
  background-color: #f0f9eb;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.tip-text {
  margin: 8px 0;
  font-size: 14px;
  color: #67c23a;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tip-text:last-child {
  color: #e6a23c;
}

/* 查询状态区域 */
.query-status-section {
  background-color: #f0f9eb;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e1f3d8;
}

.query-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.query-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #67c23a;
}

.query-progress {
  margin-bottom: 15px;
}

.progress-bar {
  height: 6px;
  background-color: #e4e7ed;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-inner {
  height: 100%;
  background-color: #67c23a;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.query-message {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #67c23a;
}

.query-icon {
  margin-right: 5px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 支付成功提示 */
.success-notice {
  margin-top: 20px;
  padding: 20px;
  background-color: #f0f9eb;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e1f3d8;
}

.success-icon {
  font-size: 40px;
  color: #67c23a;
  margin-bottom: 15px;
}

.success-title {
  font-size: 18px;
  font-weight: bold;
  color: #67c23a;
  margin-bottom: 10px;
}

.success-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 20px;
}

/* 订单超时提示 */
.expired-notice {
  margin-top: 20px;
  padding: 20px;
  background-color: #fef0f0;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #fde2e2;
}

.expired-icon {
  font-size: 40px;
  color: #f56c6c;
  margin-bottom: 15px;
}

.expired-title {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
  margin-bottom: 10px;
}

.expired-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 20px;
}

/* 响应式适配 */
@media (max-width: 767px) {
  .payment-container {
    padding: 15px;
    border-radius: 8px;
  }

  .payment-info {
    padding: 15px;
  }

  .payment-info h3 {
    font-size: 16px;
  }

  .payment-info p {
    font-size: 13px;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .pay-button {
    height: 44px;
    font-size: 16px;
  }

  .copy-btn {
    width: 100%;
    height: 44px;
    font-size: 15px;
  }

  .guide-tips {
    padding: 12px;
  }

  .tip-text {
    font-size: 13px;
  }

  .payment-notice-banner {
    padding: 15px 10px;
    margin: 15px 0;
  }

  .notice-icon {
    margin-right: 10px;
    padding: 8px;
  }

  .notice-icon svg {
    width: 24px;
    height: 24px;
  }

  .notice-title {
    font-size: 16px;
  }

  .notice-text {
    font-size: 14px;
  }
}
</style>
